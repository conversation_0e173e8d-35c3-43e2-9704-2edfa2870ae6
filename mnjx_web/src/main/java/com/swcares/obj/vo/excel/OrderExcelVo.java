package com.swcares.obj.vo.excel;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName：com.swcares.vo <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 09月27日 9:13 <br>
 * @version v1.0 <br>
 */
@Api(tags = "指令管理查询参数")
@Data
public class OrderExcelVo {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("指令类型")
    private String orderType;

    @ApiModelProperty("指令名")
    private String orderName;

    @ApiModelProperty(value = "指令描述")
    private String orderRemarks;
}
