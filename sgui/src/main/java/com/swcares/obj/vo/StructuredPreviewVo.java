package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 预览票面响应VO
 *
 * <AUTHOR>
 * @date 2025/06/30 15:30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "StructuredPreviewVo", description = "预览票面响应VO")
public class StructuredPreviewVo {

    @ApiModelProperty(value = "验证航司")
    private String vcAirline;

    @ApiModelProperty(value = "设备IATA编号")
    private String deviceIataNumber;

    @ApiModelProperty(value = "出发地")
    private String origin;

    @ApiModelProperty(value = "目的地")
    private String destination;

    @ApiModelProperty(value = "航司名称")
    private String airlineName;

    @ApiModelProperty(value = "Office编号")
    private String office;

    @ApiModelProperty(value = "设备编号")
    private String device;

    @ApiModelProperty(value = "渠道PNR")
    private String channelPnr;

    @ApiModelProperty(value = "渠道代码")
    private String channelCode;

    @ApiModelProperty(value = "旅客全名")
    private String fullName;

    @ApiModelProperty(value = "预订代理")
    private String bookingAgent;

    @ApiModelProperty(value = "背书信息")
    private String endorsementInformation;

    @ApiModelProperty(value = "航司预订参考")
    private String airlineBookingReference;

    @ApiModelProperty(value = "原始出票文本")
    private String originalIssueText;

    @ApiModelProperty(value = "航班信息列表")
    private List<FlightInfo> flights;

    @ApiModelProperty(value = "票价信息")
    private FareInfo fare;

    @ApiModelProperty(value = "是否有经停")
    private Boolean hasStopover;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FlightInfo {

        @ApiModelProperty(value = "航司代码")
        private String airline;

        @ApiModelProperty(value = "出发城市")
        private String departureCity;

        @ApiModelProperty(value = "到达城市")
        private String arrivalCity;

        @ApiModelProperty(value = "出发机场")
        private String departureAirport;

        @ApiModelProperty(value = "到达机场")
        private String arrivalAirport;

        @ApiModelProperty(value = "市场承运人")
        private String marketingCarrier;

        @ApiModelProperty(value = "是否作废航段")
        private Boolean voidSegment;

        @ApiModelProperty(value = "是否开放航段")
        private Boolean openSegment;

        @ApiModelProperty(value = "免费行李额")
        private String freeBaggageAllowance;

        @ApiModelProperty(value = "Eterm日期")
        private String etermDate;

        @ApiModelProperty(value = "Eterm时间")
        private String etermTime;

        @ApiModelProperty(value = "有效期截止日期")
        private String notValidAfterEtermDate;

        @ApiModelProperty(value = "预订状态代码")
        private String reservationStatusCode;

        @ApiModelProperty(value = "运价基础")
        private String fareBasis;

        @ApiModelProperty(value = "航班号")
        private String flightNumber;

        @ApiModelProperty(value = "市场承运人舱位代码")
        private String mcRbd;

        @ApiModelProperty(value = "是否经停")
        private Boolean isStopover;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FareInfo {

        @ApiModelProperty(value = "自动运价类型")
        private String autoFareType;

        @ApiModelProperty(value = "是否IT运价")
        private Boolean itFare;

        @ApiModelProperty(value = "运价计算文本")
        private String fareCalculationText;

        @ApiModelProperty(value = "支付方式文本")
        private String formOfPaymentText;

        @ApiModelProperty(value = "简化运价计算")
        private String simplifiedFareCalculation;

        @ApiModelProperty(value = "佣金金额")
        private String commissionAmount;

        @ApiModelProperty(value = "旅游代码")
        private String tourCode;

        @ApiModelProperty(value = "总金额")
        private AmountInfo totalAmount;

        @ApiModelProperty(value = "票面金额")
        private AmountInfo ticketAmount;

        @ApiModelProperty(value = "票面价差")
        private AmountInfo ticketSpreadPrice;

        @ApiModelProperty(value = "税费金额")
        private AmountInfo taxAmount;

        @ApiModelProperty(value = "等值金额")
        private AmountInfo equivalentAmount;

        @ApiModelProperty(value = "税费列表")
        private List<TaxInfo> taxes;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AmountInfo {

        @ApiModelProperty(value = "货币代码")
        private String currency;

        @ApiModelProperty(value = "金额")
        private String amount;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TaxInfo {

        @ApiModelProperty(value = "税费代码")
        private String taxCode;

        @ApiModelProperty(value = "税费金额")
        private String taxAmount;
    }
}
