package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 查询退票单号响应VO
 *
 * <AUTHOR>
 * @date 2025/1/2 19:00
 */
@Data
@ApiModel(value = "查询退票单号响应VO")
public class QueryRefundFormVo {

    @ApiModelProperty(value = "办公室")
    private String office;

    @ApiModelProperty(value = "IATA号")
    private String iataNo;

    @ApiModelProperty(value = "代理人")
    private String agent;

    @ApiModelProperty(value = "操作员")
    private String operator;

    @ApiModelProperty(value = "航空公司代码")
    private String airlineCode;

    @ApiModelProperty(value = "票号结束")
    private String ticketNoEnd;

    @ApiModelProperty(value = "第二票号")
    private String ticketNoSecond;

    @ApiModelProperty(value = "票号")
    private String ticketNo;

    @ApiModelProperty(value = "票号显示")
    private String ticketNoView;

    @ApiModelProperty(value = "检查")
    private String check;

    @ApiModelProperty(value = "联票号")
    private List<String> couponNo;

    @ApiModelProperty(value = "联票数量")
    private Integer conjunction;

    @ApiModelProperty(value = "旅客姓名")
    private String passengerName;

    @ApiModelProperty(value = "支付方式")
    private String payMethod;

    @ApiModelProperty(value = "货币")
    private String currency;

    @ApiModelProperty(value = "退票单货币")
    private String refundFormCurrency;

    @ApiModelProperty(value = "总退款")
    private BigDecimal grossRefund;

    @ApiModelProperty(value = "扣除费用")
    private BigDecimal deduction;

    @ApiModelProperty(value = "退款")
    private String refund;

    @ApiModelProperty(value = "佣金率")
    private BigDecimal commissionRate;

    @ApiModelProperty(value = "税费信息")
    private List<TaxInfo> taxInfos;

    @ApiModelProperty(value = "佣金")
    private BigDecimal commission;

    @ApiModelProperty(value = "总税费")
    private BigDecimal totalTaxs;

    @ApiModelProperty(value = "信用卡")
    private String creditCard;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "净退款")
    private BigDecimal netRefund;

    @ApiModelProperty(value = "退票单号")
    private String cmdNo;

    @ApiModelProperty(value = "查询成功")
    private Boolean querySuccess;

    @ApiModelProperty(value = "命令选项")
    private String cmdOption;

    @ApiModelProperty(value = "票证类型")
    private String ticketType;

    @ApiModelProperty(value = "票证管理机构代码")
    private String ticketManagementOrganizationCode;

    @ApiModelProperty(value = "旅客类型")
    private String passengerType;

    @ApiModelProperty(value = "航段信息")
    private Object segmentInfos;

    @ApiModelProperty(value = "原始票据")
    private List<String> originalTickets;

    @ApiModelProperty(value = "设备号")
    private String deviceNum;

    @ApiModelProperty(value = "二次验证因素")
    private SecondFactor secondFactor;

    @ApiModelProperty(value = "退票日期")
    private String refundDate;

    @ApiModelProperty(value = "国际标识")
    private String international;

    /**
     * 税费信息
     */
    @Data
    @ApiModel(value = "税费信息")
    public static class TaxInfo {
        @ApiModelProperty(value = "税费代码")
        private String taxCode;

        @ApiModelProperty(value = "税费金额")
        private BigDecimal taxAmount;
    }

    /**
     * 二次验证因素
     */
    @Data
    @ApiModel(value = "二次验证因素")
    public static class SecondFactor {
        @ApiModelProperty(value = "二次验证因素代码")
        private String secondFactorCode;

        @ApiModelProperty(value = "二次验证因素值")
        private String secondFactorValue;
    }
}
