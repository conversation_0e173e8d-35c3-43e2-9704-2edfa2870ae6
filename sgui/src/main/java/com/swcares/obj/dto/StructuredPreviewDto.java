package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 预览票面请求DTO
 *
 * <AUTHOR>
 * @date 2025/06/30 15:30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "StructuredPreviewDto", description = "预览票面请求DTO")
public class StructuredPreviewDto {

    @ApiModelProperty(value = "PNR编号")
    private String pnr;

    @ApiModelProperty(value = "旅客类型")
    private String passengerType;

    @ApiModelProperty(value = "旅客ID")
    private String passengerId;

    @ApiModelProperty(value = "设备编号")
    private String deviceNum;

    @ApiModelProperty(value = "国际/国内标识")
    private String international;

    @ApiModelProperty(value = "航司代码")
    private String airline;

    @ApiModelProperty(value = "添加FP标识")
    private String addFpFlag;

    @ApiModelProperty(value = "GP标识")
    private Boolean gp;

    @ApiModelProperty(value = "票据类型")
    private String ticketType;
}
