package com.swcares.controller.bkc;

import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.unified.SguiResult;
import com.swcares.obj.dto.ActivateEPIDDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2025/6/4 15:28
 */
@Slf4j
@Api(tags = "EPID接口")
@SwaggerGroup(SwaggerGroup.GroupType.BUSINESS)
@RestController
@RequestMapping("/sgui-bkc/epid")
public class EpidController {

    @ApiOperation(value = "queryByMac", notes = "queryByMac的接口")
    @GetMapping("/queryByMac")
    public SguiResult queryByMac() {
        return SguiResult.ok(null, true);
    }

    @ApiOperation(value = "activateEPID", notes = "queryByMac的接口")
    @PostMapping("/activateEPID")
    public SguiResult activateEPID(@RequestBody ActivateEPIDDto dto) {
        return SguiResult.ok(null, true);
    }

    @ApiOperation(value = "occupy", notes = "occupy的接口")
    @PostMapping("/occupy")
    public SguiResult occupy() {
        return SguiResult.ok(null, true);
    }
}
