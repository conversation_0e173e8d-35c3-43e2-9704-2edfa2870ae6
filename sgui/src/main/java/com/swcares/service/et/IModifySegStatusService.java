package com.swcares.service.et;

import com.swcares.core.exception.SguiResultException;
import com.swcares.obj.dto.ModifySegStatusDto;
import com.swcares.obj.vo.ModifySegStatusVo;

/**
 * 修改航段状态服务接口
 *
 * <AUTHOR>
 * @date 2025/06/03 16:10
 */
public interface IModifySegStatusService {

    /**
     * 修改航段状态
     *
     * @param dto 修改航段状态请求DTO
     * @return 修改结果VO
     * @throws SguiResultException 业务异常
     */
    ModifySegStatusVo modifySegmentStatus(ModifySegStatusDto dto) throws SguiResultException;
}
