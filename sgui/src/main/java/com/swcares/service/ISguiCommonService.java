package com.swcares.service;

import com.swcares.core.exception.SguiResultException;
import com.swcares.core.security.custom.CustomUserDetails;
import com.swcares.core.security.custom.UserInfo;
import com.swcares.entity.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/20 11:01
 */
public interface ISguiCommonService {

    CustomUserDetails getCurrentCustomUserDetails();

    UserInfo getCurrentUserInfo();

    String getCurrentUsername();

    /**
     * 根据PNR ID生成新的封口编号
     *
     * @param pnrId PNR ID
     * @return 新的封口编号
     * @throws SguiResultException 异常
     */
    String generateNewAtNo(String pnrId) throws SguiResultException;

    MnjxCity getCityByCityCode(String cityCode);

    MnjxAirport getAirportByCode(String airportCode);

    MnjxCity getCityByAirportCode(String airportCode);

    MnjxAirline getAirlineByCode(String airlineCode);

    /**
     * 根据城市代码获取机场ID列表
     *
     * @param cityCode 城市代码
     * @return 机场ID列表
     */
    List<String> getAirportIdList(String cityCode) throws SguiResultException;

    /**
     * 获取机场列表
     *
     * @param cityCode 城市代码
     * @return 机场列表
     */
    List<MnjxAirport> getAirportList(String cityCode);

    MnjxPlanFlight getPlanFlightByFlightNo(String flightNo, String flightDate);

    List<MnjxPlanSection> getPlanSectionListByFlightNo(String flightNo, String flightDate);

    List<MnjxOpenCabin> getOpenCabinListByFlightNo(String flightNo, String flightDate, String org, String dst);

    List<MnjxSeat> getSeatListByFlightNo(String flightNo, String flightDate, String org, String dst);
}
