package com.swcares.service.sor;

import com.swcares.core.exception.SguiResultException;
import com.swcares.obj.dto.QueryFareDomesticDto;
import com.swcares.obj.vo.QueryFareDomesticVo;

/**
 * 国内运价服务接口
 *
 * <AUTHOR>
 * @date 2025/5/16
 */
public interface IFareDomesticService {

    /**
     * 查询国内运价
     *
     * @param dto 查询国内运价参数
     * @return 查询结果
     * @throws SguiResultException 异常
     */
    QueryFareDomesticVo queryFareDomestic(QueryFareDomesticDto dto) throws SguiResultException;
}
