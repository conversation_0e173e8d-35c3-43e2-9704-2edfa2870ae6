package com.swcares.eterm.cmn.service;

import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.cmn.dto.CntdResultDto;
import com.swcares.eterm.cmn.dto.CntdRetrieveDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-7-12
 */
public interface ICntdService {

    /**
     * 解析指令
     *
     * @param cmd cmd
     * @return 解析指令
     * @throws UnifiedResultException 统一异常
     */
    CntdRetrieveDto parseCmd(String cmd) throws UnifiedResultException;

    /**
     * 处理指令业务逻辑
     *
     * @param cntdRetrieveDto cntdRetrieveDto
     * @param unifiedResult   unifiedResult
     * @return 处理指令业务逻辑
     * @throws UnifiedResultException 统一异常
     */
    List<CntdResultDto> handle(CntdRetrieveDto cntdRetrieveDto, UnifiedResult unifiedResult) throws UnifiedResultException;
}
