package com.swcares.eterm.dcs.cki.service;

import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.dcs.cki.obj.dto.FiPlanFltDto;

/**
 * FI指令接口
 *
 * <AUTHOR>
 */
public interface IFiService {

    /**
     * 查询的航班数据【业务处理】
     *
     * @param unifiedResult 参数解析
     * @return 查询的航班数据【业务处理】
     * @throws UnifiedResultException 统一异常处理
     */
    FiPlanFltDto handle(UnifiedResult unifiedResult) throws UnifiedResultException;
}
