package com.swcares.eterm.dcs.cki.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryData;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.CacheKeyConstant;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.core.util.StrUtils;
import com.swcares.eterm.dcs.cki.obj.dto.*;
import com.swcares.eterm.dcs.cki.service.IHbpaService;
import com.swcares.eterm.dcs.cki.service.IHbprService;
import com.swcares.eterm.dcs.cki.service.IPaService;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CachePut;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * HBPA业务层
 * Created on 2018年6月29日<br>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@CacheConfig(cacheNames = CacheKeyConstant.CACHE_ETERM_KEY)
public class HbpaServiceImpl implements IHbpaService {

    /**
     * HBPA:航班号
     * /航班日期
     * 舱等
     * 目的地
     * (#)?
     * 旅客HBNB序号(候补旅客姓名)
     * (,选项)?
     */
    private static final Pattern HBPA_PATTER = Pattern.compile("(\\w{5,6})" +
            "/(\\d{2}[A-Z]{3}\\d{2})" +
            "([A-Z])" +
            "([A-Z]{3})" +
            "(((;)?(#)?(\\d+)([A-Z/]+)?" +
            "((,[\\u4e00-\\u9fa5\\w\\s-/]+)*))*)");

    @Resource
    private IPaService iPaService;

    @Resource
    private IHbprService iHbprService;

    @Override
    public List<HbpaCmdDto> parseCmd(String cmd) throws UnifiedResultException {
        if (cmd.contains("\r- ")) {
            cmd = cmd.replace("\r- ", "");
        } else if (cmd.contains("\r-")) {
            cmd = cmd.replace("\r-", "");
        }
        if (cmd.contains(StrUtils.CR)) {
            cmd = cmd.replace("\r", "");
        }
        List<HbpaCmdDto> hbpaCmdDtoList = new ArrayList<>();
        String[] split = cmd.split(":");
        String param = split[1].trim();
        if (ReUtil.isMatch(HBPA_PATTER, param)) {
            List<String> allGroups = ReUtil.getAllGroups(HBPA_PATTER, param);
            String flightNo = allGroups.get(1);
            String flightDate = allGroups.get(2);
            String sellCabin = allGroups.get(3);
            String dstCityCode = allGroups.get(4);
            String multiPsgAndOption = allGroups.get(5);
            if (multiPsgAndOption.startsWith(StrUtils.SEMICOLON)) {
                throw new UnifiedResultException(Constant.FORMAT);
            }
            // 判断第一个旅客是否输入了普通行李或AVIH行李参数
            boolean firstPsgHaveLuggage = false;
            String[] psgSplit = multiPsgAndOption.split(";");
            for (int j = 0; j < psgSplit.length; j++) {
                String psg = psgSplit[j];
                HbpaCmdDto hbpaCmdDto = new HbpaCmdDto();
                hbpaCmdDto.setCmd(cmd);
                String[] inPsgSplit = psg.split(",");
                List<String> list = Arrays.asList(inPsgSplit);
                List<String> optionList = new ArrayList<>();
                for (int i = 0; i < list.size(); i++) {
                    if (i == 0) {
                        String psgIndexAndName = list.get(i);
                        if (psgIndexAndName.startsWith("#")) {
                            hbpaCmdDto.setAdv(true);
                            if (psgIndexAndName.substring(1).matches("^(\\d+)([A-Z]+)$")) {
                                optionList.add(psgIndexAndName.substring(1));
                            } else if (psgIndexAndName.substring(1).matches("^\\d+$")) {
                                hbpaCmdDto.setHbnbNo(Integer.parseInt(psgIndexAndName.substring(1)));
                            } else {
                                optionList.add(psgIndexAndName.substring(1));
                            }
                        } else if (psgIndexAndName.matches("^\\d+$")) {
                            hbpaCmdDto.setHbnbNo(Integer.parseInt(psgIndexAndName));
                        } else {
                            optionList.add(psgIndexAndName);
                        }
                    } else {
                        String option = list.get(i);
                        // 这种";"输入方式，CHD数字只能是1
                        if (option.startsWith("CHD") && Integer.parseInt(option.substring(3)) != 1) {
                            throw new UnifiedResultException(Constant.CHILD_COUNT_CONFLICT);
                        }
                        if (ReUtil.isMatch("(AVIH)?\\d+/\\d+", option)) {
                            if (j == 0) {
                                firstPsgHaveLuggage = true;
                            }
                            // 其他旅客如果输入了行李参数，根据第一个旅客是否输入行李参数返回不同的报错信息
                            else {
                                if (firstPsgHaveLuggage) {
                                    throw new UnifiedResultException(Constant.DUP_IS_FOUND);
                                } else {
                                    throw new UnifiedResultException(Constant.BAGS_NOT_ALLOWED_FOR_POOLED_DONOR);
                                }
                            }
                        }
                        optionList.add(option);
                    }
                }
                hbpaCmdDto.setFlightNo(flightNo);
                hbpaCmdDto.setFlightDate(flightDate);
                hbpaCmdDto.setSellCabin(sellCabin);
                hbpaCmdDto.setDstCityCode(dstCityCode);
                hbpaCmdDto.setOptionList(optionList);
                hbpaCmdDtoList.add(hbpaCmdDto);
            }
        } else {
            throw new UnifiedResultException(Constant.FORMAT);
        }
        return hbpaCmdDtoList;
    }

    @Override
    @CachePut(key = "'hbpaResult' + #memoryData.memoryDataId")
    public List<PaResultDto> handle(MemoryData memoryData, List<HbpaCmdDto> hbpaCmdDtoList) throws UnifiedResultException {
        List<PaResultDto> paResultDtoList = new ArrayList<>();
        // 接收候补旅客时，不使用缓存
        if (hbpaCmdDtoList.stream().anyMatch(p -> p.getOptionList().contains("URES") || p.getOptionList().contains("NREC"))) {
            iPaService.handleUres(hbpaCmdDtoList, paResultDtoList);
            return paResultDtoList;
        }

        List<PdNmDto> pdNmDtos = new ArrayList<>();
        HbprDto hbprDto = new HbprDto();
        hbprDto.setFlightNo(hbpaCmdDtoList.get(0).getFlightNo());
        hbprDto.setFlightDate(hbpaCmdDtoList.get(0).getFlightDate());
        PdInfoDto pdInfoDto = new PdInfoDto();
        List<String> hbnbNoList = hbpaCmdDtoList.stream()
                .map(p -> StrUtil.fill(StrUtil.toString(p.getHbnbNo()), '0', 4, true))
                .collect(Collectors.toList());
        iHbprService.checkoutCacheHit(hbprDto, pdNmDtos, pdInfoDto, hbnbNoList, memoryData, false, false, false);
        if (StrUtil.isEmpty(pdInfoDto.getFlightNo())) {
            throw new UnifiedResultException(Constant.NO_RECORD);
        }

        List<PdNmDto> pdDtos = new ArrayList<>();
        this.constructFromPd(paResultDtoList, pdInfoDto, hbpaCmdDtoList, pdDtos);

        if (CollUtil.isEmpty(paResultDtoList)) {
            throw new UnifiedResultException(Constant.NO_DISPLAY);
        }
        synchronized (pdDtos.get(0)) {
            iPaService.parseAndUpdateData(paResultDtoList);
        }
        return paResultDtoList;
    }

    @Override
    public void print(List<PaResultDto> paResultDtoList) {
        iPaService.print(paResultDtoList);
    }

    /**
     * Title: constructFromPd
     * Description: 从PD结果构建数据
     *
     * @param paResultDtoList paResultDtoList
     * @param pdInfoDto       pdInfoDto
     * @param hbpaCmdDtoList  hbpaCmdDtoList
     * @return 从PD结果构建数据
     * <AUTHOR>
     * @date 2022/8/10 10:35
     */
    private void constructFromPd(List<PaResultDto> paResultDtoList, PdInfoDto pdInfoDto, List<HbpaCmdDto> hbpaCmdDtoList, List<PdNmDto> pdDtos) throws UnifiedResultException {
        List<PdNmDto> pdNmDtoList = pdInfoDto.getNms();
        for (HbpaCmdDto hbpaCmdDto : hbpaCmdDtoList) {
            int hbnbNo = hbpaCmdDto.getHbnbNo();
            if (ObjectUtil.isNull(hbnbNo)) {
                throw new UnifiedResultException(Constant.ITEM);
            }
            List<PdNmDto> collect = pdNmDtoList.stream()
                    .filter(p -> StrUtil.isNotEmpty(p.getPsgNum()) && Integer.parseInt(p.getPsgNum().trim()) == hbnbNo)
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(collect)) {
                throw new UnifiedResultException(Constant.NUMBER_ERROR);
            }
            PdNmDto pdNmDto = collect.get(0);
            pdDtos.add(pdNmDto);
            PaResultDto paResultDto = new PaResultDto();
            paResultDto.setHbpa(true);
            paResultDto.setCmd(hbpaCmdDto.getCmd());
            paResultDto.setAdv(hbpaCmdDto.isAdv());
            paResultDto.setOptionList(hbpaCmdDto.getOptionList());
            // 构建航班信息
            paResultDto.setFlightDate(DateUtils.com2ymd(hbpaCmdDto.getFlightDate()));
            paResultDto.setDstAirport(hbpaCmdDto.getDstCityCode());
            paResultDto.setPlaneType(StrUtil.isNotEmpty(pdInfoDto.getPlaneType()) ? pdInfoDto.getPlaneType().split("/")[0] : null);
            paResultDto.setPlaneVersion(StrUtil.isNotEmpty(pdInfoDto.getPlaneType()) ? pdInfoDto.getPlaneType().split("/")[1] : null);
            paResultDto.setGate(StrUtil.isNotEmpty(pdInfoDto.getGtd()) ? (pdInfoDto.getGtd().split("/").length > 1 ? pdInfoDto.getGtd().split("/")[1] : pdInfoDto.getGtd()) : "????");
            paResultDto.setBdt(pdInfoDto.getBoarding());
            paResultDto.setSd(pdInfoDto.getEstimateOff());
            paResultDto.setEd(pdInfoDto.getActualOff());
            paResultDto.setSa("");
            paResultDto.setFt("");
            // 构建旅客信息数据
            paResultDto.setQueryName(pdNmDto.getQueryName().matches("\\d+.+") ? pdNmDto.getQueryName().substring(1) : pdNmDto.getQueryName());
            paResultDto.setQueryName(StrUtil.fill(paResultDto.getQueryName(), ' ', 13, false));
            paResultDto.setPnrNmId(pdNmDto.getPnrNmId());
            paResultDto.setCnin(StrUtil.isNotEmpty(pdNmDto.getIsCnin()));
            paResultDto.setName(pdNmDto.getName());
            paResultDto.setOInterlink(pdNmDto.getOInterlink());
            paResultDto.setOrgAirport(pdNmDto.getOrg());
            paResultDto.setSellCabin(pdNmDto.getSellCabin());
            paResultDto.setCabinClass(pdNmDto.getCabinClass());
            paResultDto.setPsgNum(StrUtil.fill(StrUtil.toString(Integer.parseInt(pdNmDto.getPsgNum())), ' ', 4, false));
            paResultDto.setShareFlightNo(pdNmDto.getShareFlight());
            paResultDto.setFlightNo(StrUtil.isEmpty(pdNmDto.getCarrierFlight()) ? pdInfoDto.getFlightNo() : pdNmDto.getCarrierFlight());
            paResultDto.setAirlineCode(paResultDto.getFlightNo().substring(0, 2));

            paResultDtoList.add(paResultDto);
        }
    }
}