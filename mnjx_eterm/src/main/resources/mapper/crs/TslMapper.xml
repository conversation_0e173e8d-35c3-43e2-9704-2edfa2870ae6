<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.eterm.crs.mapper.TslMapper">


    <select id="retrieveTicketInfo" resultType="com.swcares.eterm.crs.obj.dto.TslTicket">
        SELECT mtor.si_no as agentNo,
        mpf.pat_type,
        mtor.settlement_code as settlementCode,
        mtor.ticket_no as tktNumber,
        mtor.operate_time as operateTime,
        mtor.ticket_status_1 AS mtorStatus1,
        mtor.ticket_status_2 AS mtorStatus2,
        mpn.pnr_nm_id as pnrNmId,
        mtn.nm_xn_id as pnrXnId,
        mpn.pnr_id as pnrId,
        mps.pnr_seg_no as pnrSegNo,
        CASE
        WHEN mtor.ticket_status_1 ='REFUNDED' THEN ''
        else (SELECT pnr_crs FROM mnjx_pnr WHERE pnr_id = mpn.pnr_id )
        END AS crsPnr,
        IFNULL(mpf.f_price, mrt.net_refund) as collection,
        IFNULL(mpf.x_price, mrt.cn_price + mrt.yq_price) taxs,
        IFNULL(FORMAT(mpf.f_price * mpf.c_rate, 2), mrt.comm) AS comm,
        CASE
        WHEN mtor.ticket_status_1 ='REFUNDED' THEN 'ET-REFUND'
        ELSE (concat((select mc.city_code from mnjx_airport ma LEFT JOIN mnjx_city mc ON mc.city_id=ma.city_id WHERE
        ma.airport_code=mps.org),' ',(select mc.city_code from mnjx_airport ma LEFT JOIN mnjx_city mc ON
        mc.city_id=ma.city_id WHERE ma.airport_code=mps.dst)))
        END AS orgDst,
        mrt.refund_no as refNo
        FROM mnjx_ticket_operate_record mtor
        LEFT JOIN mnjx_pnr_nm_ticket mpnt ON mpnt.ticket_no = mtor.ticket_no
        LEFT JOIN mnjx_pnr_nm_tn mtn ON mtn.tn_id = mpnt.pnr_nm_tn_id
        LEFT JOIN mnjx_pnr_nm mpn ON mpn.pnr_nm_id = mtn.pnr_nm_id
        LEFT JOIN mnjx_pnr_seg mps ON mps.pnr_id=mpn.pnr_id
        LEFT JOIN mnjx_pnr_fn mpf ON mpn.pnr_id = mpf.pnr_id
        AND mtor.ticket_status_1 != 'REFUNDED'
        LEFT JOIN mnjx_refund_ticket mrt ON mrt.ticket_no = mtor.ticket_no
        AND mtor.ticket_status_1 = 'REFUNDED'
        WHERE 1=1
        and mtor.operate_time REGEXP (select CURDATE())
        AND mtn.printer_id = #{printId}
        <if test="dataType !=null and dataType.length()==4">
            and si_no=#{dataType}
        </if>
        <if test="dataType !=null and dataType.length()==2">
            and mtn.issued_airline=#{dataType}
        </if>
        order by mtor.ticket_no ASC ,mps.pnr_seg_no ASC
    </select>

    <select id="retrieveTicketInfoIn" resultType="com.swcares.eterm.crs.obj.dto.TslTicket">
        SELECT mtor.si_no as agentNo,
        mpf.pat_type,
        mtor.settlement_code as settlementCode,
        mtor.ticket_no as tktNumber,
        mtor.operate_time as operateTime,
        mtor.ticket_status_1 AS mtorStatus1,
        mtor.ticket_status_2 AS mtorStatus2,
        mpn.pnr_nm_id as pnrNmId,
        mnx.nm_xn_id as pnrXnId,
        mpn.pnr_id as pnrId,
        mps.pnr_seg_no as pnrSegNo,
        CASE
        WHEN mtor.ticket_status_1 ='REFUNDED' THEN ''
        else (SELECT pnr_crs FROM mnjx_pnr WHERE pnr_id = mpn.pnr_id )
        END AS crsPnr,
        IFNULL(mpf.f_price, mrt.net_refund) as collection,
        IFNULL(mpf.x_price, mrt.cn_price + mrt.yq_price) taxs,
        IFNULL(FORMAT(mpf.f_price * mpf.c_rate, 2), mrt.comm) AS comm,
        CASE
        WHEN mtor.ticket_status_1 ='REFUNDED' THEN 'ET-REFUND'
        ELSE (concat((select mc.city_code from mnjx_airport ma LEFT JOIN mnjx_city mc ON mc.city_id=ma.city_id WHERE
        ma.airport_code=mps.org),' ',(select mc.city_code from mnjx_airport ma LEFT JOIN mnjx_city mc ON
        mc.city_id=ma.city_id WHERE ma.airport_code=mps.dst)))
        END AS orgDst,
        mrt.refund_no as refNo
        FROM mnjx_ticket_operate_record mtor
        LEFT JOIN mnjx_pnr_nm_ticket mpnt ON mpnt.ticket_no = mtor.ticket_no
        LEFT JOIN mnjx_pnr_nm_tn mtn ON mtn.tn_id = mpnt.pnr_nm_tn_id
        Left join mnjx_nm_xn mnx on mtn.nm_xn_id = mnx.nm_xn_id
        LEFT JOIN mnjx_pnr_nm mpn ON mnx.pnr_nm_id = mpn.pnr_nm_id
        LEFT JOIN mnjx_pnr_seg mps ON mps.pnr_id=mpn.pnr_id
        LEFT JOIN mnjx_pnr_fn mpf ON mpn.pnr_id = mpf.pnr_id
        AND mtor.ticket_status_1 != 'REFUNDED'
        LEFT JOIN mnjx_refund_ticket mrt ON mrt.ticket_no = mtor.ticket_no
        AND mtor.ticket_status_1 = 'REFUNDED'
        WHERE 1=1
        and mtor.operate_time REGEXP (select CURDATE())
        AND mtn.printer_id = #{printId}
        <if test="dataType !=null and dataType.length()==4">
            and si_no=#{dataType}
        </if>
        <if test="dataType !=null and dataType.length()==2">
            and mtn.issued_airline=#{dataType}
        </if>
        and mpf.pat_type = 'IN'
        order by mtor.ticket_no ASC ,mps.pnr_seg_no ASC
    </select>
</mapper>