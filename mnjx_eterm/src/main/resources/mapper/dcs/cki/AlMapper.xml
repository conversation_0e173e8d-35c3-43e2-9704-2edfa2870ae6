<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.eterm.dcs.cki.mapper.AlMapper">
    <select id="retrieveSeatStatus" resultType="com.swcares.entity.MnjxSeat">
		SELECT
			ms.*
		FROM
			mnjx_flight mf
			LEFT JOIN mnjx_tcard mt ON mf.flight_id = mt.flight_id
			LEFT JOIN mnjx_plan_flight mpf ON mpf.tcard_id = mt.tcard_id
			LEFT JOIN mnjx_plan_section mps ON mps.plan_flight_id = mpf.plan_flight_id
			LEFT JOIN mnjx_airport dep ON dep.airport_id = mps.dep_apt_id
			LEFT JOIN mnjx_airport arr ON arr.airport_id = mps.arr_apt_id
			LEFT JOIN mnjx_open_cabin moc ON moc.plan_section_id = mps.plan_section_id
			LEFT JOIN mnjx_seat ms ON ms.open_cabin_id = moc.open_cabin_id
		WHERE
			mf.flight_no = #{alDto.flightNo}
	        AND mpf.flight_date = #{alDto.flightDate}
	        AND ms.seat_no = #{alDto.seatNo}
			AND (dep.airport_code = #{alDto.dep}
				OR arr.airport_code = #{alDto.arr})
	</select>
</mapper>