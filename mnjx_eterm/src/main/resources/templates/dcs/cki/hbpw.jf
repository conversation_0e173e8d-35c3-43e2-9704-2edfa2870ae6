#set(firstRes = results[0], arg = args[0], null)
HBPW: #(arg) #(results.length)#(wrap())
AV #(firstRes.avLayout)    PAD #(firstRes.padLayout)#(wrap())
#if(firstRes.planeType??)#(firstRes.planeType)/#(firstRes.planeVersion) #end#[[GTD/]]##(firstRes.gate) POS/GATE#if(firstRes.estimateBoarding??) BDT#(firstRes.estimateBoarding)#end#if(firstRes.estimateOff??) SD#(firstRes.estimateOff)#end#if(firstRes.actualOff??) ED#(firstRes.actualOff)#end#if(firstRes.actualArr??)  SA#(firstRes.actualArr)#end#if(firstRes.ft??)  FT#(firstRes.ft)#end#(wrap())
#for(res:results)
#set(ssrListSize = res.otherSsrList.size(), null)
#if(res.mnjxPsgCki.ures?? && res.mnjxPsgCki.ures == '1')
#(fillIndex((for.index + 1), 3, true)). #(res.queryName)#(res.groupName)#if(res.mnjxPsgCki.ckiStatus == 'DL')DELETED  #elseif(res.mnjxPsgCki.ckiStatus == 'ACC')BN#(res.mnjxPsgCki.aboardNo)   #elseif(res.mnjxPsgCki.ckiStatus == 'SB')SB#(res.mnjxPsgCki.hbNo)   #else        #end#if(res.stcr??)#(res.stcr)   #elseif(res.jmp??)#(res.jmp)   #elseif(res.mnjxPsgSeat.psgSeat??)#(res.mnjxPsgSeat.psgSeat)   #elseif(res.mnjxPsgCki.cap?? && res.mnjxPsgCki.cap == '1')CAP  #else      #end#if((res.mnjxPsgCki.ures?? && res.mnjxPsgCki.ures == '1') || res.isExst())URES #end#if(ssrListSize > 0)#for(i = 0; i < ssrListSize; i++)#(res.otherSsrList.get(i)) #if(for.index == 3)#break#end#end#(wrap())#end 
#else
#(fillIndex((for.index + 1), 3, true)). #(res.queryName)#(res.groupName)#if(res.mnjxPsgCki.ckiStatus == 'DL')DELETED  #elseif(res.mnjxPsgCki.ckiStatus == 'ACC')BN#(res.mnjxPsgCki.aboardNo)   #elseif(res.mnjxPsgCki.ckiStatus == 'SB')SB#(res.mnjxPsgCki.hbNo)   #else        #end#if(res.stcr??)#(res.stcr)   #elseif(res.jmp??)#(res.jmp)   #elseif(res.mnjxPsgSeat.psgSeat??)#(res.mnjxPsgSeat.psgSeat)   #elseif(res.mnjxPsgCki.cap?? && res.mnjxPsgCki.cap == '1')CAP  #else      #end#if((res.mnjxPsgCki.ures?? && res.mnjxPsgCki.ures == '1') || res.isExst())URES #end#if(ssrListSize > 0)#for(i = 0; i < ssrListSize; i++)#(res.otherSsrList.get(i)) #if(for.index == 3)#break#end#end#(wrap())#end  
#end
#if(ssrListSize > 4)                                        #for(i = 4; i < ssrListSize; i++)#(res.otherSsrList.get(i)) #if(for.index == 3)#break#end#end#(wrap())#end
#if(ssrListSize > 8)                                        #for(i = 8; i < ssrListSize; i++)#(res.otherSsrList.get(i)) #if(for.index == 3)#break#end#end#(wrap())#end
#if(res.ff??)                                        #(res.ff)#(wrap())#end
#if(res.tktNo??)                                        ET TKNE/#(res.tktNo)#(wrap())#end
#if(res.infTktNo??)                                        ET TKNE/#(res.infTktNo)#(wrap())#end
#if(res.foidNo??)                                        FOID/#(res.foidNo)#(wrap())#end
#if(res.isCnin??)                                        CNIN/#(res.name) #(wrap())#end
#if(res.msg??) MSG-#(res.msg)#(wrap())#end
#if(res.infName??)INF-#(res.infName)#(wrap())#end
#if(res.psm?? && res.isVip() && res.chld??)PSM-#(res.psm) /#(res.chld) /VIP#(wrap())#elseif(res.isVip())PSM-#if(res.psm??)#(res.psm) #end#if(res.chld??)/#(res.chld) #end/VIP#(wrap())#elseif(res.psm??)PSM-#(res.psm)#if(res.isVip()) /VIP#end#if(res.chld??) /#(res.chld)#end#(wrap())#elseif(res.chld??)PSM-#if(res.psm??)#(res.psm) #end#if(res.isVip()) /VIP#end/#(res.chld)#(wrap())#end
#if(res.shareFltNo??)MARKETING FLIGHT:#(res.shareFltNo) FARE CLASS:#(res.cabinClass)#(wrap())#end
#if(res.pil??)PIL-#(res.pil)#(wrap())#end
#if(res.isVip())ATTENTION:THIS IS A VIP PASSENGER#(wrap())#end
#if(res.phone??)CTC-#(res.officeNo)-#(res.fltDate)-#(res.issuedTime)-T #(res.phone)#(wrap())#end
#if(res.spml??)SPML-#(res.spml)#(wrap())#end
#if(res.otherWc??)#(res.otherWc)-#(wrap())#end
#if(res.meda??)#(res.meda)#(wrap())#end
#if(res.interlineList.size() > 0 && !res.isShow())#for(oi:res.interlineList)#(oi)#if(for.index != res.interlineList.size()-1)#(wrap())#end#end#end

#for(lcRes:res.followList)
 TRANSFER #(lcRes.org) #(lcRes.fltNo)/#(lcRes.fltDate)
AV #(lcRes.avLayout)    PAD #(lcRes.padLayout)#(wrap())
#if(lcRes.planeType??)#(lcRes.planeType)/#(lcRes.planeVersion) #end#[[GTD/]]##(lcRes.gate) POS/GATE#if(lcRes.estimateBoarding??) BDT#(lcRes.estimateBoarding)#end#if(lcRes.estimateOff??) SD#(lcRes.estimateOff)#end#if(lcRes.actualOff??) ED#(lcRes.actualOff)#end#if(lcRes.actualArr??)  SA#(lcRes.actualArr)#end#if(firstRes.ft??)  FT#(lcRes.ft)#end#(wrap())
#set(lcSsrListSize = lcRes.otherSsrList.size(), null)
#if(lcRes.mnjxPsgCki.ures?? && lcRes.mnjxPsgCki.ures == '1')
#(fillIndex((for.index + 1), 3, true)). #(lcRes.queryName)#(lcRes.groupName)#if(lcRes.mnjxPsgCki.ckiStatus == 'DL')DELETED  #elseif(lcRes.mnjxPsgCki.ckiStatus == 'ACC')BN#(lcRes.mnjxPsgCki.aboardNo)   #elseif(lcRes.mnjxPsgCki.ckiStatus == 'SB')SB#(lcRes.mnjxPsgCki.hbNo)   #else        #end#if(lcRes.stcr??)#(res.stcr)   #elseif(lcRes.jmp??)#(lcRes.jmp)   #elseif(lcRes.mnjxPsgSeat.psgSeat??)#(lcRes.mnjxPsgSeat.psgSeat)   #elseif(lcRes.mnjxPsgCki.cap?? && lcRes.mnjxPsgCki.cap == '1')CAP  #else       #end#if((lcRes.mnjxPsgCki.ures?? && lcRes.mnjxPsgCki.ures == '1') || lcRes.isExst())URES #end#if(lcSsrListSize > 0)#for(i = 0; i < lcSsrListSize; i++)#(lcRes.otherSsrList.get(i)) #if(for.index == 3)#break#end#end#(wrap())#end
#else
#(fillIndex((for.index + 1), 3, true)). #(lcRes.queryName)#(lcRes.groupName)#if(lcRes.mnjxPsgCki.ckiStatus == 'DL')DELETED  #elseif(lcRes.mnjxPsgCki.ckiStatus == 'ACC')BN#(lcRes.mnjxPsgCki.aboardNo)   #elseif(lcRes.mnjxPsgCki.ckiStatus == 'SB')SB#(lcRes.mnjxPsgCki.hbNo)   #else        #end#if(lcRes.stcr??)#(res.stcr)   #elseif(lcRes.jmp??)#(lcRes.jmp)   #elseif(lcRes.mnjxPsgSeat.psgSeat??)#(lcRes.mnjxPsgSeat.psgSeat)   #elseif(lcRes.mnjxPsgCki.cap?? && lcRes.mnjxPsgCki.cap == '1')CAP  #else       #end#if((lcRes.mnjxPsgCki.ures?? && lcRes.mnjxPsgCki.ures == '1') || lcRes.isExst())URES #end#if(lcSsrListSize > 0)#for(i = 0; i < lcSsrListSize; i++)#(lcRes.otherSsrList.get(i)) #if(for.index == 3)#break#end#end#(wrap())#end
#end
#if(lcSsrListSize > 4)                                        #for(i = 4; i < lcSsrListSize; i++)#(lcRes.otherSsrList.get(i)) #if(for.index == 3)#break#end#end#(wrap())#end
#if(lcSsrListSize > 8)                                        #for(i = 8; i < lcSsrListSize; i++)#(lcRes.otherSsrList.get(i)) #if(for.index == 3)#break#end#end#(wrap())#end
#if(lcRes.ff??)                                        #(lcRes.ff)#(wrap())#end
#if(lcRes.tktNo??)                                        ET TKNE/#(lcRes.tktNo)#(wrap())#end
#if(lcRes.infTktNo??)                                        ET TKNE/#(lcRes.infTktNo)#(wrap())#end
#if(lcRes.foidNo??)                                        FOID/#(lcRes.foidNo)#(wrap())#end
#if(lcRes.isCnin??)                                        CNIN/#(lcRes.name) #(wrap())#end
#if(lcRes.msg??) MSG-#(lcRes.msg)#(wrap())#end
#if(lcRes.infName??)INF-#(lcRes.infName)#(wrap())#end
#if(lcRes.psm?? && lcRes.isVip() && lcRes.chld??)PSM-#(lcRes.psm) /#(lcRes.chld) /VIP#(wrap())#elseif(lcRes.isVip())PSM-#if(lcRes.psm??)#(lcRes.psm) #end#if(lcRes.chld??)/#(lcRes.chld) #end/VIP#(wrap())#elseif(lcRes.psm??)PSM-#(lcRes.psm)#if(lcRes.isVip()) /VIP#end#if(lcRes.chld??) /#(lcRes.chld)#end#(wrap())#elseif(lcRes.chld??)PSM-#if(lcRes.psm??)#(lcRes.psm) #end#if(lcRes.isVip()) /VIP#end/#(lcRes.chld)#(wrap())#end
#if(lcRes.shareFltNo??)MARKETING FLIGHT:#(lcRes.shareFltNo) FARE CLASS:#(lcRes.cabinClass)#(wrap())#end
#if(lcRes.pil??)PIL-#(lcRes.pil)#(wrap())#end
#if(lcRes.isVip())ATTENTION:THIS IS A VIP PASSENGER#(wrap())#end
#if(lcRes.phone??)CTC-#(lcRes.officeNo)-#(lcRes.fltDate)-#(lcRes.issuedTime)-T #(lcRes.phone)#(wrap())#end
#if(lcRes.spml??)SPML-#(lcRes.spml)#(wrap())#end
#if(lcRes.otherWc??)#(lcRes.otherWc)-#(wrap())#end
#if(res.meda??)#(res.meda)#(wrap())#end
#end

#end