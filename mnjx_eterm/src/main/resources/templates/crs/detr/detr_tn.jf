#set(ticketDto = resDto[0].ticketDto,segList= resDto[0].segList)
ISSUED BY:#(ticketDto.issuedAirlineEn)          ORG/DST:#(ticketDto.org)/#(ticketDto.dst)      BSP-D #(wrap())
E/R: #(ticketDto.ei)#(wrap())
TOUR CODE: #(wrap())
PASSENGER: #((ticketDto.adlName??)?ticketDto.adlName:ticketDto.xnName)#(wrap())
EXCH:#(ticketDto.oldTicketNo)                               CONJ TKT:#(ticketDto.couponTicketNo)#(wrap())
#for(r:segList)
#(r)#(wrap())
#end
#if("REFUNDED"!=ticketDto.ticketStatus1)
#if(ticketDto.fcInputValue!=null)
FC:#(ticketDto.fcInputValue)#(wrap())
#end
#end
FARE:           #(ticketDto.fare)|FOP:#(ticketDto.payType)#(wrap())
TAX:            #(ticketDto.taxCn)|OI: #(ticketDto.oi)#(wrap())
TAX:            #(ticketDto.taxYq)|#(wrap())
#if(ticketDto.oc!=null)
TAX:            #(ticketDto.oc)|#(wrap())
#end
TOTAL:          #(ticketDto.totalPrice)|TKTN: #(format("{}-{}",subPre(ticketDto.ticketNo,3),subSuf(ticketDto.ticketNo,3)))
