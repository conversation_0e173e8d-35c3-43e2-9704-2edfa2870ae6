package com.swcares.core.util;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;

import java.util.List;
import java.util.function.Consumer;
import java.util.stream.Collectors;

class DateUtilsTest {
    public static void main(String[] args) {
        List<DateTime> dateRange = DateUtils.rangeToList(DateUtils.ymd2Date("2022-06-01"), DateUtils.ymd2Date("2022-08-30"), DateField.DAY_OF_MONTH);
        dateRange.forEach(new Consumer<DateTime>() {
            @Override
            public void accept(DateTime dateTime) {
                System.out.println(DateUtils.dayOfWeek(dateTime)-1);
            }
        });
        List<String> dateStrs = dateRange.stream().map(dateTime -> DateUtils.date2ymd(dateTime)).collect(Collectors.toList());
        //dateStrs.forEach(System.out::println);
    }
}
