package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_pnr_nm_ticket")
public class MnjxPnrNmTicket extends Model<MnjxPnrNmTicket> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "nm_ticket_id", type = IdType.ASSIGN_ID)
    private String nmTicketId;

    @ApiModelProperty(value = "成人或者儿童姓名ID")
    @TableField("pnr_nm_tn_id")
    private String pnrNmTnId;

    @ApiModelProperty(value = "票号-13位票号")
    @TableField("ticket_no")
    private String ticketNo;

    @ApiModelProperty(value = "该客票第一航段组客票使用状态")
    @TableField("ticket_status_1")
    private String ticketStatus1;

    @ApiModelProperty(value = "该客票第二航段组客票使用状态")
    @TableField("ticket_status_2")
    private String ticketStatus2;

    @ApiModelProperty(value = "旅游代码")
    @TableField("tour_code")
    private String tourCode;

    @ApiModelProperty(value = "是否已打印行程单，1已打印，0未打印")
    @TableField("receipt_print")
    private String receiptPrint;

    @ApiModelProperty(value = "航段1记录ID")
    @TableField("s1_id")
    private String s1Id;

    @ApiModelProperty(value = "航段2记录ID")
    @TableField("s2_id")
    private String s2Id;

    @ApiModelProperty(value = "")
    @TableField("input_value")
    private String inputValue;
    @ApiModelProperty(value = "航段1旅客hnbn编号")
    @TableField("hbnb_1")
    private String hbnb1;

    @ApiModelProperty(value = "航段2旅客hnbn编号")
    @TableField("hbnb_2")
    private String hbnb2;
    @ApiModelProperty(value = "是否是ET票")
    @TableField("is_et")
    private String isEt;

    @Override
    protected Serializable pkVal() {
        return this.nmTicketId;
    }

}
