<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxPlanSectionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxPlanSection">
        <id column="plan_section_id" property="planSectionId"/>
        <result column="plan_flight_id" property="planFlightId"/>
        <result column="gate" property="gate"/>
        <result column="is_last_section" property="isLastSection"/>
        <result column="dep_apt_id" property="depAptId"/>
        <result column="arr_apt_id" property="arrAptId"/>
        <result column="estimate_off" property="estimateOff"/>
        <result column="estimate_off_change" property="estimateOffChange"/>
        <result column="estimate_arr" property="estimateArr"/>
        <result column="estimate_arr_change" property="estimateArrChange"/>
        <result column="estimate_boarding" property="estimateBoarding"/>
        <result column="estimate_boarding_change" property="estimateBoardingChange"/>
        <result column="actual_off" property="actualOff"/>
        <result column="actual_off_change" property="actualOffChange"/>
        <result column="actual_arr" property="actualArr"/>
        <result column="actual_arr_change" property="actualArrChange"/>
        <result column="actual_boarding" property="actualBoarding"/>
        <result column="actual_boarding_change" property="actualBoardingChange"/>
        <result column="actual_takeoff" property="actualTakeoff"/>
        <result column="actual_takeoff_change" property="actualTakeoffChange"/>
        <result column="actual_land" property="actualLand"/>
        <result column="actual_land_change" property="actualLandChange"/>
        <result column="meal_code" property="mealCode"/>
        <result column="meal_airport" property="mealAirport"/>
        <result column="uwt" property="uwt"/>
        <result column="uaw" property="uaw"/>
        <result column="standby_bag_number" property="standbyBagNumber"/>
        <result column="grade_book_number" property="gradeBookNumber"/>
        <result column="grade_standby_number" property="gradeStandbyNumber"/>
        <result column="grade_ck_number" property="gradeCkNumber"/>
        <result column="grade_added_psg" property="gradeAddedPsg"/>
        <result column="grade_added_crew" property="gradeAddedCrew"/>
        <result column="grade_eat" property="gradeEat"/>
        <result column="goshow_limit" property="goshowLimit"/>
        <result column="id_limit" property="idLimit"/>
        <result column="hl_limit" property="hlLimit"/>
        <result column="ck_initial_shutdown" property="ckInitialShutdown"/>
        <result column="ck_mid_shutdown" property="ckMidShutdown"/>
        <result column="ck_all_shutdown" property="ckAllShutdown"/>
        <result column="ck_type" property="ckType"/>
        <result column="ck_condition" property="ckCondition"/>
        <result column="max_board_no" property="maxBoardNo"/>
        <result column="board_status" property="boardStatus"/>
        <result column="boarding_number" property="boardingNumber"/>
        <result column="pre_estimate_off" property="preEstimateOff"/>
        <result column="pre_estimate_arr" property="preEstimateArr"/>
        <result column="plane_id" property="planeId"/>
        <result column="pre_plane_id" property="prePlaneId"/>
        <result column="arr_position" property="arrPosition"/>
        <result column="off_position" property="offPosition"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        plan_section_id, plan_flight_id, gate, is_last_section, dep_apt_id, arr_apt_id, estimate_off, estimate_off_change, estimate_arr, estimate_arr_change,
        estimate_boarding, estimate_boarding_change, actual_off, actual_off_change, actual_arr, actual_arr_change, actual_boarding, actual_boarding_change,
        actual_takeoff, actual_takeoff_change, actual_land, actual_land_change, meal_code, meal_airport, uwt, uaw, standby_bag_number,
        grade_book_number, grade_standby_number, grade_ck_number, grade_added_psg, grade_added_crew, grade_eat, goshow_limit, id_limit, hl_limit, ck_initial_shutdown,
        ck_mid_shutdown, ck_all_shutdown, ck_type, ck_condition, max_board_no, board_status, boarding_number, pre_estimate_off, pre_estimate_arr,
        plane_id, pre_plane_id, arr_position, off_position
    </sql>

</mapper>
